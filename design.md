<!--
 * @Author: 黄忠平 <EMAIL>
 * @Date: 2025-07-31 22:04:57
 * @LastEditors: 黄忠平 <EMAIL>
 * @LastEditTime: 2025-07-31 22:05:06
 * @FilePath: /test/design.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
# AI 直播间高光切片机 · 产品设计文档
**版本：v0.1 | 作者：AI + 你 | 日期：2025-07-31**

---

## 1. 产品定位
| 项目 | 内容 |
|---|---|
| **一句话描述** | 上传 1 条直播录像，3 分钟后拿到 5-8 条可直接发小红书的竖屏高光。 |
| **目标用户** | 抖音/快手/视频号/小红书主播、MCN、直播切片号运营者 |
| **核心价值** | 省时：人工剪 1 h → AI 3 min；省钱：0 剪辑师；合规：字幕 + 竖屏一键完成。 |

---

## 2. 需求场景
1. **主播下播后**急需 15 秒爆点二次分发。  
2. **切片号运营**每天 10 条直播素材，人工剪不过来。  
3. **品牌自播**需快速产出种草短视频。

---

## 3. 功能列表（MVP）
| 功能 | 入口 | 输出 | 技术实现 |
|---|---|---|---|
| 上传视频 | Web 拖拽 / API | 等待队列 | Streamlit / FastAPI |
| 高光检测 | 语音峰值 + 弹幕密度 | 5-8 个时间戳 | Whisper + 滑动窗口 |
| 竖屏裁剪 | 自动人脸居中 | 9:16 1080×1920 | moviepy crop |
| 字幕生成 | 中文硬字幕 | `.srt` + 烧录 | autosub |
| 打包下载 | 一键 ZIP | 6 条 MP4 | zipfile |
| 进度通知 | WebSocket / 邮件 | 完成提醒 | FastAPI + Redis |

---

## 4. 技术架构
